# Dart SDK Manual Install Script (after manual download)
Write-Host "=== Dart SDK Manual Install Script ===" -ForegroundColor Green
Write-Host ""

# Set variables
$dartDir = "D:\download\dartEnviron"
$dartSdkZip = "$dartDir\dart-sdk.zip"

# Check if SDK zip exists
if (-not (Test-Path $dartSdkZip)) {
    Write-Host "Error: Cannot find Dart SDK zip file!" -ForegroundColor Red
    Write-Host "Please download Dart SDK from https://dart.dev/get-dart" -ForegroundColor Yellow
    Write-Host "1. Download Windows x64 version" -ForegroundColor Cyan
    Write-Host "2. Rename to 'dart-sdk.zip'" -ForegroundColor Cyan
    Write-Host "3. Put in: $dartDir" -ForegroundColor Cyan
    exit 1
}

Write-Host "Found Dart SDK zip file: $dartSdkZip" -ForegroundColor Green

# Extract SDK
Write-Host "Extracting Dart SDK..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $dartSdkZip -DestinationPath $dartDir -Force
    Write-Host "Extraction completed!" -ForegroundColor Green
}
catch {
    Write-Host "Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Set environment variables
$dartBinPath = "$dartDir\dart-sdk\bin"
Write-Host "Setting environment variables..." -ForegroundColor Yellow

# Get current user PATH environment variable
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# Check if Dart path is already added
if ($currentPath -notlike "*$dartBinPath*") {
    # Add Dart path to PATH
    $newPath = "$currentPath;$dartBinPath"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "Added Dart to user PATH environment variable" -ForegroundColor Green
} else {
    Write-Host "Dart path already exists in PATH" -ForegroundColor Green
}

# Refresh current session environment variables
$env:PATH = "$env:PATH;$dartBinPath"

Write-Host ""
Write-Host "=== Installation Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Dart SDK location: $dartDir\dart-sdk" -ForegroundColor Cyan
Write-Host "Dart executable: $dartBinPath\dart.exe" -ForegroundColor Cyan
Write-Host ""

# Verify installation
Write-Host "Verifying installation..." -ForegroundColor Yellow
try {
    $dartVersion = & "$dartBinPath\dart.exe" --version 2>&1
    Write-Host "Dart version: $dartVersion" -ForegroundColor Green
    Write-Host ""
    Write-Host "Installation successful! You can now use Dart!" -ForegroundColor Green
}
catch {
    Write-Host "Warning: Cannot verify Dart installation" -ForegroundColor Yellow
    Write-Host "Please restart command line and run 'dart --version' to verify" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Note: If 'dart' command is not available in current window:" -ForegroundColor Yellow
Write-Host "1. Close current command line window" -ForegroundColor Cyan
Write-Host "2. Open a new command line window" -ForegroundColor Cyan
Write-Host "3. Run 'dart --version' to verify installation" -ForegroundColor Cyan
Write-Host ""

# Test with your index.dart file
Write-Host "Testing with your index.dart file..." -ForegroundColor Yellow
$indexDartPath = "d:\workpace\test\index.dart"
if (Test-Path $indexDartPath) {
    Write-Host "Found index.dart, trying to run it..." -ForegroundColor Cyan
    try {
        & "$dartBinPath\dart.exe" run $indexDartPath
    }
    catch {
        Write-Host "Error running index.dart: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "index.dart not found at $indexDartPath" -ForegroundColor Yellow
}
